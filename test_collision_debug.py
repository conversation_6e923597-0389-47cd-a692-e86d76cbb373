#!/usr/bin/env python3

# Test collision detection logic directly
import sys
sys.path.append('.')
from agent import detect_move_collisions, resolve_move_collisions

def test_collision_detection():
    print("Testing collision detection...")
    
    # Test case 1: Target collision (both agents moving to same destination)
    planned_moves1 = {
        1: {'from': (1, 1), 'to': (2, 2)},
        2: {'from': (3, 3), 'to': (2, 2)}  # Same destination
    }
    
    collisions1 = detect_move_collisions(planned_moves1)
    print(f"Test 1 - Target collision: {collisions1}")
    
    # Test case 2: Swap collision (agents swapping positions)
    planned_moves2 = {
        1: {'from': (1, 1), 'to': (2, 2)},
        2: {'from': (2, 2), 'to': (1, 1)}  # Swapping positions
    }
    
    collisions2 = detect_move_collisions(planned_moves2)
    print(f"Test 2 - Swap collision: {collisions2}")
    
    # Test case 3: No collision
    planned_moves3 = {
        1: {'from': (1, 1), 'to': (2, 2)},
        2: {'from': (3, 3), 'to': (4, 4)}  # Different destinations
    }
    
    collisions3 = detect_move_collisions(planned_moves3)
    print(f"Test 3 - No collision: {collisions3}")
    
    # Test collision resolution
    my_agents = [
        {'agent_id': 1, 'x': 1, 'y': 1, 'wetness': 10},
        {'agent_id': 2, 'x': 3, 'y': 3, 'wetness': 20}
    ]
    
    resolved, cancelled = resolve_move_collisions(my_agents, planned_moves1, None)
    print(f"Collision resolution - Resolved: {resolved}")
    print(f"Collision resolution - Cancelled: {cancelled}")

if __name__ == "__main__":
    test_collision_detection()
